cmake_minimum_required(VERSION 3.14)

# find_package(yaEngineNext REQUIRED)
include(${CMAKE_SOURCE_DIR}/cmake/yaEngineNextConfig.cmake)

# Include protocol configuration and setup ASN.1 protocols
include(${CMAKE_CURRENT_SOURCE_DIR}/protocols.cmake)
setup_asn1_protocols()

# Add ASN.1 subdirectory (for any remaining setup)
add_subdirectory(asn1)

#
# plugins
#
addEngineNextPlugin(udp ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_udp.c
)

addEngineNextPlugin(rtp ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_rtp.c
)

addEngineNextPlugin(dns ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_dns.c
)

addEngineNextPlugin(hwzzeth ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_hwzzeth.c
)

addEngineNextPlugin(hwzz ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_hwzz.c
)

addEngineNextPlugin(rt ${CMAKE_SOURCE_DIR}/bin/plugins
  TRAILER
  SOURCES dissector_trailer_rt.c
)

addEngineNextPlugin(sip ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES       dissector_sip.c
  RAGEL_SOURCES parser_sip.rl
)

# Automatically configure plugins for all ASN.1 protocols
configure_asn1_plugins()
