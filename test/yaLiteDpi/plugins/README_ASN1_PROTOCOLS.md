# ASN.1 协议支持指南

本文档说明如何在yaEngineNext中添加新的ASN.1协议支持。

## 架构概述

ASN.1协议解析采用统一管理的架构：
- 所有协议配置在 `protocols.cmake` 文件中
- ASN.1定义文件存放在 `asn1/protocols/` 目录下
- 自动生成的解析器代码存放在构建目录的 `asn1/generated/` 下
- 插件通过统一的接口链接ASN.1库

## 添加新协议的步骤

### 1. 添加ASN.1定义文件

在 `test/yaLiteDpi/plugins/asn1/protocols/` 目录下创建新的协议目录：

```bash
mkdir test/yaLiteDpi/plugins/asn1/protocols/your_protocol
```

将ASN.1定义文件（.asn）放入该目录：

```bash
cp your_protocol.asn test/yaLiteDpi/plugins/asn1/protocols/your_protocol/
```

### 2. 注册协议

编辑 `test/yaLiteDpi/plugins/protocols.cmake` 文件，在 `ASN1_PROTOCOLS` 列表中添加新协议：

```cmake
# List of protocols that require ASN.1 parsing
set(ASN1_PROTOCOLS
    snmp
    your_protocol  # 添加这一行
    # Add new protocols here, one per line
)
```

### 3. 创建解析器插件

创建新的解析器文件：

```bash
cp test/yaLiteDpi/plugins/dissector_snmp.c test/yaLiteDpi/plugins/dissector_your_protocol.c
```

修改解析器代码以适应新协议的需求。

### 4. 配置插件构建

在 `test/yaLiteDpi/plugins/CMakeLists.txt` 中添加新插件的配置：

```cmake
# Your Protocol plugin with ASN.1 support
get_asn1_library(your_protocol YOUR_PROTOCOL_ASN1_LIB)
if(YOUR_PROTOCOL_ASN1_LIB)
    addEngineNextPlugin(your_protocol ${CMAKE_SOURCE_DIR}/bin/plugins
      SOURCES dissector_your_protocol.c
      LINK_LIBRARIES "-Wl,--whole-archive" ${YOUR_PROTOCOL_ASN1_LIB} "-Wl,--no-whole-archive"
    )
    message(STATUS "Your Protocol plugin configured with ASN.1 support")
else()
    message(WARNING "Your Protocol plugin will be built without ASN.1 support")
    addEngineNextPlugin(your_protocol ${CMAKE_SOURCE_DIR}/bin/plugins
      SOURCES dissector_your_protocol.c
    )
endif()
```

### 5. 构建和测试

重新配置和构建项目：

```bash
rm -rf build
cmake -S . -B build
cmake --build build
```

## 文件结构

```
test/yaLiteDpi/plugins/
├── protocols.cmake                    # 协议配置文件（主要修改这里）
├── CMakeLists.txt                     # 插件构建配置
├── asn1/
│   ├── asn1_functions.cmake          # ASN.1生成函数
│   ├── CMakeLists.txt                 # ASN.1构建配置
│   └── protocols/
│       ├── snmp/
│       │   └── snmp.asn              # SNMP ASN.1定义
│       └── your_protocol/            # 新协议目录
│           └── your_protocol.asn     # 新协议ASN.1定义
├── dissector_snmp.c                  # SNMP解析器实现
└── dissector_your_protocol.c         # 新协议解析器实现
```

## 优势

1. **统一管理**: 所有ASN.1协议在一个文件中配置
2. **最少修改**: 添加新协议只需修改 `protocols.cmake` 文件
3. **自动化**: ASN.1代码生成和库链接完全自动化
4. **依赖管理**: 自动处理构建依赖关系
5. **错误处理**: 优雅处理缺失的ASN.1文件或构建失败

## 注意事项

1. 确保ASN.1定义文件语法正确
2. 新协议名称应该是有效的CMake目标名称（字母、数字、下划线）
3. 如果ASN.1文件有特殊的生成需求，可能需要修改 `asn1_functions.cmake` 中的 `EXPECTED_GENERATED_FILES` 列表
4. 测试时建议先清理构建目录以确保所有依赖正确重建

## 示例：添加LDAP协议支持

1. 创建目录：`mkdir test/yaLiteDpi/plugins/asn1/protocols/ldap`
2. 添加文件：`cp ldap.asn test/yaLiteDpi/plugins/asn1/protocols/ldap/`
3. 修改 `protocols.cmake`：在 `ASN1_PROTOCOLS` 中添加 `ldap`
4. 创建解析器：`cp dissector_snmp.c dissector_ldap.c`
5. 修改 `CMakeLists.txt`：添加LDAP插件配置
6. 构建：`cmake --build build`

这样就完成了新协议的添加，整个过程只需要修改一个配置文件和添加必要的源文件。
