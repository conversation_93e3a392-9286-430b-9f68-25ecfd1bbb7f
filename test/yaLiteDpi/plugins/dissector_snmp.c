#include <yaEngineNext/nxt_engine.h>
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>
#include <string.h>
#include <stdlib.h>
#include <arpa/inet.h>

// Include ASN.1 generated headers (will be generated at build time)
#include "Message.h"
#include "PDUs.h"
#include "PDU.h"
#include "VarBindList.h"
#include "VarBind.h"
#include "ObjectName.h"
#include "ObjectSyntax.h"
#include "Trap-PDU.h"
#include "ber_decoder.h"

#define PROTO_NAME     "snmp"

// Helper function to convert ASN.1 OBJECT IDENTIFIER to string
static char* oid_to_string(const OBJECT_IDENTIFIER_t *oid, char *buffer, size_t buffer_size)
{
    if (!oid || !buffer || buffer_size == 0) {
        return NULL;
    }

    buffer[0] = '\0';

    // Use asn1c's OBJECT_IDENTIFIER_print_arc function if available
    // For now, we'll create a simple representation
    snprintf(buffer, buffer_size, "OID[%zu bytes]", oid->size);
    return buffer;
}

// Helper function to extract string from OCTET_STRING
static char* octet_string_to_string(const OCTET_STRING_t *octet_str, char *buffer, size_t buffer_size)
{
    if (!octet_str || !buffer || buffer_size == 0) {
        return NULL;
    }

    size_t copy_len = (octet_str->size < buffer_size - 1) ? octet_str->size : buffer_size - 1;
    memcpy(buffer, octet_str->buf, copy_len);
    buffer[copy_len] = '\0';
    return buffer;
}

// Helper function to process variable bindings
static void process_varbind_list(const VarBindList_t *varbind_list, precord_t *precord)
{
    if (!varbind_list || !precord) {
        return;
    }

    ya_fvalue_t* fvArray = precord_put(precord, "varbind_array", array);

    for (int i = 0; i < varbind_list->list.count; i++) {
        VarBind_t *varbind = varbind_list->list.array[i];
        if (!varbind) continue;

        char oid_str[256] = {0};
        char value_str[256] = {0};

        // Convert OID to string
        oid_to_string(&varbind->name, oid_str, sizeof(oid_str));

        // Process value based on ObjectSyntax
        switch (varbind->value.present) {
            case ObjectSyntax_PR_simple:
                // Handle SimpleSyntax
                switch (varbind->value.choice.simple.present) {
                    case SimpleSyntax_PR_number:
                        snprintf(value_str, sizeof(value_str), "%ld", varbind->value.choice.simple.choice.number);
                        break;
                    case SimpleSyntax_PR_string:
                        octet_string_to_string(&varbind->value.choice.simple.choice.string, value_str, sizeof(value_str));
                        break;
                    case SimpleSyntax_PR_object:
                        oid_to_string(&varbind->value.choice.simple.choice.object, value_str, sizeof(value_str));
                        break;
                    case SimpleSyntax_PR_empty:
                        snprintf(value_str, sizeof(value_str), "NULL");
                        break;
                    default:
                        snprintf(value_str, sizeof(value_str), "unknown_simple");
                        break;
                }
                break;
            case ObjectSyntax_PR_application_wide:
                // Handle ApplicationSyntax
                snprintf(value_str, sizeof(value_str), "application_wide");
                break;
            default:
                snprintf(value_str, sizeof(value_str), "unknown");
                break;
        }

        // Add to array
        ya_fvalue_t* fvTable = precord_sub_append(precord, fvArray, table, YA_FT_TABLE);
        precord_sub_put(precord, fvTable, "oid", string, YA_FT_STRING, oid_str);
        precord_sub_put(precord, fvTable, "value", string, YA_FT_STRING, value_str);
    }
}

static
int snmp_dissect(nxt_engine_t *engine, nxt_session_t *session, nxt_mbuf_t *mbuf)
{
    // Check minimum length for SNMP message
    if (nxt_mbuf_get_length(mbuf) < 10) {
        return NXT_DISSECT_ST_VERIFY_FAILED;
    }

    precord_t *precord = nxt_session_create_record(engine, session);
    precord_layer_put_new_layer_cache(precord, PROTO_NAME);

    // Get raw data from mbuf
    const uint8_t *data = nxt_mbuf_get_raw(mbuf, 0);
    size_t data_len = nxt_mbuf_get_length(mbuf);

    // Decode SNMP message using asn1c
    Message_t *message = NULL;
    asn_dec_rval_t decode_result;

    decode_result = ber_decode(0, &asn_DEF_Message, (void **)&message, data, data_len);

    if (decode_result.code != RC_OK) {
        // Decoding failed
        nxt_session_destroy_record(engine, session, precord);
        return NXT_DISSECT_ST_VERIFY_FAILED;
    }

    // Successfully decoded, consumed bytes = decode_result.consumed
    int lret = (int)decode_result.consumed;

    // Extract basic message information
    precord_put(precord, "version", uinteger, message->version);

    // Extract community string
    char community_str[256] = {0};
    octet_string_to_string(&message->community, community_str, sizeof(community_str));
    precord_put(precord, "community", string, community_str);

    // Process PDU based on type
    switch (message->data.present) {
        case PDUs_PR_get_request:
            precord_put(precord, "pdu_type", string, "GetRequest");
            {
                PDU_t *pdu = &message->data.choice.get_request;
                precord_put(precord, "request_id", uinteger, pdu->request_id);
                precord_put(precord, "error_status", uinteger, pdu->error_status);
                precord_put(precord, "error_index", uinteger, pdu->error_index);
                process_varbind_list(&pdu->variable_bindings, precord);
            }
            break;

        case PDUs_PR_get_next_request:
            precord_put(precord, "pdu_type", string, "GetNextRequest");
            {
                PDU_t *pdu = &message->data.choice.get_next_request;
                precord_put(precord, "request_id", uinteger, pdu->request_id);
                precord_put(precord, "error_status", uinteger, pdu->error_status);
                precord_put(precord, "error_index", uinteger, pdu->error_index);
                process_varbind_list(&pdu->variable_bindings, precord);
            }
            break;

        case PDUs_PR_get_response:
            precord_put(precord, "pdu_type", string, "GetResponse");
            {
                PDU_t *pdu = &message->data.choice.get_response;
                precord_put(precord, "request_id", uinteger, pdu->request_id);
                precord_put(precord, "error_status", uinteger, pdu->error_status);
                precord_put(precord, "error_index", uinteger, pdu->error_index);
                process_varbind_list(&pdu->variable_bindings, precord);
            }
            break;

        case PDUs_PR_set_request:
            precord_put(precord, "pdu_type", string, "SetRequest");
            {
                PDU_t *pdu = &message->data.choice.set_request;
                precord_put(precord, "request_id", uinteger, pdu->request_id);
                precord_put(precord, "error_status", uinteger, pdu->error_status);
                precord_put(precord, "error_index", uinteger, pdu->error_index);
                process_varbind_list(&pdu->variable_bindings, precord);
            }
            break;

        case PDUs_PR_trap:
            precord_put(precord, "pdu_type", string, "Trap");
            {
                Trap_PDU_t *trap = &message->data.choice.trap;
                char enterprise_str[256] = {0};
                oid_to_string(&trap->enterprise, enterprise_str, sizeof(enterprise_str));
                precord_put(precord, "enterprise", string, enterprise_str);

                // Agent address (NetworkAddress is OCTET STRING of size 4 for IPv4)
                if (trap->agent_addr.size == 4) {
                    uint32_t ip_addr = *(uint32_t*)trap->agent_addr.buf;
                    char ip_str[16];
                    inet_ntop(AF_INET, &ip_addr, ip_str, sizeof(ip_str));
                    precord_put(precord, "agent_addr", string, ip_str);
                }

                precord_put(precord, "generic_trap", uinteger, trap->generic_trap);
                precord_put(precord, "specific_trap", uinteger, trap->specific_trap);
                precord_put(precord, "time_stamp", uinteger, trap->time_stamp);
                process_varbind_list(&trap->variable_bindings, precord);
            }
            break;

        default:
            precord_put(precord, "pdu_type", string, "Unknown");
            break;
    }

    nxt_session_post_event(engine, session, NXT_EVENT_PACKET_MESSAGE, mbuf, precord);

    // Free the decoded message
    ASN_STRUCT_FREE(asn_DEF_Message, message);

    nxt_session_destroy_record(engine, session, precord);
    return lret;
}
static int snmp_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db) {
  /* 注册 schema */
  pschema_t *pschema = pschema_register_proto(db, PRECORD_NOT_IMPORT_TEMPLATE, PROTO_NAME, "snmp");

  // Basic SNMP message fields
  pschema_register_field(pschema, "version", YA_FT_UINT32, "SNMP Version");
  pschema_register_field(pschema, "community", YA_FT_STRING, "Community String");
  pschema_register_field(pschema, "pdu_type", YA_FT_STRING, "PDU Type");

  // PDU fields
  pschema_register_field(pschema, "request_id", YA_FT_UINT32, "Request ID");
  pschema_register_field(pschema, "error_status", YA_FT_UINT32, "Error Status");
  pschema_register_field(pschema, "error_index", YA_FT_UINT32, "Error Index");

  // Trap-specific fields
  pschema_register_field(pschema, "enterprise", YA_FT_STRING, "Enterprise OID");
  pschema_register_field(pschema, "agent_addr", YA_FT_STRING, "Agent Address");
  pschema_register_field(pschema, "generic_trap", YA_FT_UINT32, "Generic Trap Type");
  pschema_register_field(pschema, "specific_trap", YA_FT_UINT32, "Specific Trap");
  pschema_register_field(pschema, "time_stamp", YA_FT_UINT32, "Time Stamp");

  // Variable bindings array
  pschema_register_field(pschema, "varbind_array", YA_FT_ARRAY, "Variable Bindings");
  pschema_register_field(pschema, "identifier", YA_FT_UINT32, "identifier");
  pschema_register_field(pschema, "context_name", YA_FT_STRING, "context_name");
  pschema_register_field(pschema, "context_engine", YA_FT_UINT32, "context_engine");
  pschema_register_field(pschema, "msgReportFlag", YA_FT_UINT32, "msgReportFlag");
  pschema_register_field(pschema, "msgEncryptFlag", YA_FT_UINT32, "msgEncryptFlag");
  pschema_register_field(pschema, "msgAuthFlag", YA_FT_UINT32, "msgAuthFlag");
  pschema_register_field(pschema, "sec_mode", YA_FT_UINT32, "sec_mode");
  pschema_register_field(pschema, "engine_boots", YA_FT_UINT32, "engine_boots");
  pschema_register_field(pschema, "engine_time", YA_FT_UINT32, "engine_time");
  pschema_register_field(pschema, "engine_max_msg_size", YA_FT_UINT32, "engine_max_msg_size");
  pschema_register_field(pschema, "engine_id", YA_FT_BYTES, "engine_id");
  pschema_register_field(pschema, "engineIdFormat", YA_FT_UINT32, "engineIdFormat");
  pschema_register_field(pschema, "engineIdData", YA_FT_BYTES, "engineIdData");
  pschema_register_field(pschema, "engineIdDataStr", YA_FT_STRING, "engineIdDataStr");
  pschema_register_field(pschema, "sysName", YA_FT_STRING, "sysName");
  pschema_register_field(pschema, "sysDesc", YA_FT_STRING, "sysDesc");
  pschema_register_field(pschema, "Total_OID", YA_FT_UINT32, "Total_OID");

  return 0;
}

static nxt_dissector_def_t gDissectorDef = {
    .name = "snmp",
    .type = NXT_DISSECTOR_TYPE_APP,
    .schemaRegFun = snmp_schema_reg,
    .dissectFun = snmp_dissect,
    .mountAt =
        {
            NXT_MNT_NUMBER("udp", 161),
            NXT_MNT_NUMBER("udp", 162),
            NXT_MNT_END,
        },
};

NXT_DISSECTOR_INIT(snmp) { nxt_dissector_register(&gDissectorDef); }
