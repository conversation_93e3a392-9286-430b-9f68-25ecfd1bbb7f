cmake_minimum_required(VERSION 3.14)

# project
project(yaNxtUnitTest LANGUAGES C CXX VERSION 0.0.1)

set(CMAKE_C_STANDARD 11)
set(CMAKE_CXX_STANDARD 14)

add_compile_options(-Wall -Wextra -Werror)

# import PkgConfig
set(ENV{PKG_CONFIG_PATH} "$ENV{PKG_CONFIG_PATH}:/usr/local/lib64/pkgconfig")
find_package(PkgConfig REQUIRED)
find_package(GTest REQUIRED)
pkg_check_modules(glib2 REQUIRED IMPORTED_TARGET glib-2.0)

#
# yaNxtUnitTest
#
add_executable(yaNxtUnitTest
  main_test.cpp
  basic_test.cpp
  timer_test.cpp
  recognizer_test.cpp
  session_test.cpp
  parser_rtsp_test.cpp
  parser_http_test.cpp
  dissector_http_test.cpp
  snmp_asn1_test.cpp
)

# 单元测试可能对内部实现进行测试(例如 nxt_Session, http_dissector), 这需要它看到 src 目录
target_include_directories(yaNxtUnitTest PUBLIC ${CMAKE_SOURCE_DIR}/src)

# Add ASN.1 include directory for SNMP tests
target_include_directories(yaNxtUnitTest PUBLIC ${CMAKE_BINARY_DIR}/test/yaLiteDpi/plugins/asn1/generated/snmp)

target_link_libraries(yaNxtUnitTest
  PUBLIC yaEngineNext
  PUBLIC gtest
  PUBLIC gmock
  PUBLIC PkgConfig::glib2
)

# Link ASN.1 libraries if available
if(TARGET asn1_snmp)
    target_link_libraries(yaNxtUnitTest PUBLIC asn1_snmp)
    add_dependencies(yaNxtUnitTest asn1_snmp)
    message(STATUS "Unit tests linked with ASN.1 SNMP library")
else()
    message(WARNING "Unit tests will be built without ASN.1 SNMP support")
endif()

#
# 暂不将 unit test 自动加入到 test 中；
# 测试流程是:
# 1.单元测试：make unit
# 2.功能测试：make test
# gtest_discover_tests(yaNxtUnitTest)
#

#
# target: 运行单元测试
# make unit
#
add_custom_target(unit
  $(wrapper) $<TARGET_FILE:yaNxtUnitTest> $(args)
)
